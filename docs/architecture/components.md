# Components

Based on the architectural patterns, tech stack, and data models defined earlier, here are the major logical components for the My Pomodoro application:

## Timer Component

**Responsibility:** Manages the core Pomodoro timer functionality, including starting, pausing, and resetting the timer, as well as handling the transition between work and break sessions.

**Implementation Details:**

- Located at: `apps/web/src/components/Timer/`
- Uses requestAnimationFrame for accurate timing (as per coding standards)
- Displays time in MM:SS format
- Default work duration: 25 minutes (1500 seconds)
- Visual indication when timer is active (color change)
- Start/Pause and Reset controls

**Key Interfaces:**

- startTimer(): void
- pauseTimer(): void
- resetTimer(): void
- setTimerDuration(duration: number): void
- onTick(callback: (timeLeft: number) => void): void
- onComplete(callback: () => void): void

**Dependencies:**

- Audio Service (for session completion alerts)
- State Management (for timer state)
- timeUtils (for time formatting)

**Technology Stack:**

- React with TypeScript
- Web APIs (requestAnimationFrame for accurate timing)
- styled-components for UI

## Session Tracker Component

**Responsibility:** Tracks and displays Pomodoro sessions, including daily session counts and historical data.

**Key Interfaces:**

- recordSession(session: Session): void
- getTodaySessions(): Session[]
- getSessionsByDate(date: Date): Session[]
- getSessionCount(date: Date): number

**Dependencies:**

- Data Access Layer (for storing/retrieving session data)
- State Management (for current session tracking)

**Technology Stack:**

- React with TypeScript
- IndexedDB for data persistence
- styled-components for UI

## Settings Component

**Responsibility:** Manages user preferences for timer durations, audio alerts, and other customizable options.

**Key Interfaces:**

- getPreferences(): UserPreferences
- updatePreferences(preferences: UserPreferences): void
- resetToDefaults(): void

**Dependencies:**

- Data Access Layer (for storing/retrieving preferences)
- State Management (for current preferences)

**Technology Stack:**

- React with TypeScript
- IndexedDB for data persistence
- styled-components for UI

## Audio Service

**Responsibility:** Handles audio alerts for session transitions and other notifications.

**Key Interfaces:**

- playSessionComplete(): void
- setVolume(volume: number): void
- enableAudio(enabled: boolean): void

**Dependencies:**

- Web Audio API

**Technology Stack:**

- TypeScript
- Web Audio API

## Data Access Layer

**Responsibility:** Provides a unified interface for storing and retrieving data from IndexedDB.

**Key Interfaces:**

- saveSession(session: Session): Promise<void>
- getSessions(date?: Date): Promise<Session[]>
- savePreferences(preferences: UserPreferences): Promise<void>
- getPreferences(): Promise<UserPreferences>

**Dependencies:**

- IndexedDB API

**Technology Stack:**

- TypeScript
- IndexedDB

## Component Diagrams

```mermaid
graph TD
    A[User Interface] --> B[Timer Component]
    A --> C[Session Tracker Component]
    A --> D[Settings Component]

    B --> E[Audio Service]
    B --> F[State Management]

    C --> G[Data Access Layer]
    C --> F

    D --> G
    D --> F

    G --> H[IndexedDB]

    subgraph Frontend Components
        A
        B
        C
        D
        E
        F
    end

    subgraph Backend/Data Layer
        G
        H
    end
```
