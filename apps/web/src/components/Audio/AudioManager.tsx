import React, { createContext, useContext, useState } from 'react';
import { playSessionTransitionSound, playBeep } from '../../utils/audioUtils';

// Define the audio context type
interface AudioContextType {
  isAudioEnabled: boolean;
  audioVolume: number;
  toggleAudio: () => void;
  setAudioVolume: (volume: number) => void;
  playSessionTransition: () => void;
  playBeepSound: () => void;
}

// Create the audio context
const AudioContext = createContext<AudioContextType | undefined>(undefined);

// Audio provider component
export const AudioProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAudioEnabled, setIsAudioEnabled] = useState<boolean>(true);
  const [audioVolume, setAudioVolume] = useState<number>(50);

  const toggleAudio = () => {
    setIsAudioEnabled(prev => !prev);
  };

  const playSessionTransition = () => {
    if (isAudioEnabled) {
      playSessionTransitionSound(audioVolume);
    }
  };

  const playBeepSound = () => {
    if (isAudioEnabled) {
      playBeep(audioVolume);
    }
  };

  return (
    <AudioContext.Provider
      value={{
        isAudioEnabled,
        audioVolume,
        toggleAudio,
        setAudioVolume,
        playSessionTransition,
        playBeepSound
      }}
    >
      {children}
    </AudioContext.Provider>
  );
};

// Custom hook to use audio context
export const useAudio = () => {
  const context = useContext(AudioContext);
  if (context === undefined) {
    throw new Error('useAudio must be used within an AudioProvider');
  }
  return context;
};

export default AudioProvider;