import styled from 'styled-components';

export const TimerContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1.5rem;
`;

export const SessionTypeIndicator = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'sessionType',
})<{ sessionType: 'work' | 'break' }>`
  font-size: 1.5rem;
  font-weight: bold;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  background-color: ${props => (props.sessionType === 'work' ? '#28a745' : '#17a2b8')};
  color: white;
  text-transform: uppercase;
  letter-spacing: 1px;
`;

export const TimeDisplay = styled.div.withConfig({
  shouldForwardProp: (prop) => !['sessionType', 'isActive'].includes(prop),
})<{ isActive: boolean; sessionType?: 'work' | 'break' }>`
  font-size: 4rem;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  color: ${props => {
    if (props.isActive) return '#e74c3c';
    if (props.sessionType === 'work') return '#28a745';
    if (props.sessionType === 'break') return '#17a2b8';
    return '#333';
  }};
  text-shadow: ${props =>
    props.isActive ? '0 0 10px rgba(231, 76, 60, 0.3)' : 'none'};
  transition: all 0.3s ease;
`;

export const TimerControls = styled.div`
  display: flex;
  gap: 1rem;
`;
