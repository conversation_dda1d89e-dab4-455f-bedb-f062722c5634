import styled from 'styled-components';

interface StyledButtonProps {
  variant: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  size: 'small' | 'medium' | 'large';
  disabled: boolean;
}

export const StyledButton = styled.button<StyledButtonProps>`
  /* Base styles */
  border: none;
  border-radius: 4px;
  cursor: ${props => (props.disabled ? 'not-allowed' : 'pointer')};
  font-family: inherit;
  font-weight: 600;
  transition: all 0.3s ease;
  opacity: ${props => (props.disabled ? 0.6 : 1)};
  
  /* Size variants */
  ${props => {
    switch (props.size) {
      case 'small':
        return `
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        `;
      case 'large':
        return `
          padding: 1rem 2rem;
          font-size: 1.125rem;
        `;
      case 'medium':
      default:
        return `
          padding: 0.75rem 1.5rem;
          font-size: 1rem;
        `;
    }
  }}
  
  /* Color variants */
  ${props => {
    switch (props.variant) {
      case 'secondary':
        return `
          background-color: #6c757d;
          color: white;
          
          &:hover:not(:disabled) {
            background-color: #5a6268;
          }
          
          &:active:not(:disabled) {
            transform: translateY(1px);
          }
        `;
      case 'success':
        return `
          background-color: #28a745;
          color: white;
          
          &:hover:not(:disabled) {
            background-color: #218838;
          }
          
          &:active:not(:disabled) {
            transform: translateY(1px);
          }
        `;
      case 'warning':
        return `
          background-color: #ffc107;
          color: #212529;
          
          &:hover:not(:disabled) {
            background-color: #e0a800;
          }
          
          &:active:not(:disabled) {
            transform: translateY(1px);
          }
        `;
      case 'danger':
        return `
          background-color: #dc3545;
          color: white;
          
          &:hover:not(:disabled) {
            background-color: #c82333;
          }
          
          &:active:not(:disabled) {
            transform: translateY(1px);
          }
        `;
      case 'primary':
      default:
        return `
          background-color: #007bff;
          color: white;
          
          &:hover:not(:disabled) {
            background-color: #0069d9;
          }
          
          &:active:not(:disabled) {
            transform: translateY(1px);
          }
        `;
    }
  }}
`;