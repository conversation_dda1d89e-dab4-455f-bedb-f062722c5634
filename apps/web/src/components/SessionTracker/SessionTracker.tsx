import React from 'react';
import { Session } from '../../types/session';

interface SessionTrackerProps {
  sessions: Session[];
}

const SessionTracker: React.FC<SessionTrackerProps> = ({ sessions }) => {
  return (
    <div>
      <h2>Session History</h2>
      {sessions.length === 0 ? (
        <p>No sessions recorded yet.</p>
      ) : (
        <ul>
          {sessions.map((session) => (
            <li key={session.id}>
              {session.type === 'work' ? 'Work Session' : 'Break Time'} - 
              {new Date(session.startTime).toLocaleTimeString()} to 
              {session.endTime ? new Date(session.endTime).toLocaleTimeString() : 'Ongoing'} -
              {session.completed ? 'Completed' : 'Incomplete'}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default SessionTracker;