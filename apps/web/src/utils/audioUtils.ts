/**
 * Audio utility functions for the My Pomodoro application
 * 
 * These functions handle audio playback for session transitions and other app events.
 */

/**
 * Play a beep sound using the Web Audio API
 * @param volume - Volume level (0-100)
 */
export const playBeep = (volume: number = 50): void => {
  try {
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.type = 'sine';
    oscillator.frequency.value = 80; // A5 note
    gainNode.gain.value = volume / 100;
    
    oscillator.start();
    oscillator.stop(audioContext.currentTime + 0.2); // 200ms beep
  } catch (error) {
    console.warn('Failed to play beep sound:', error);
  }
};

/**
 * Play a session transition sound
 * @param volume - Volume level (0-100)
 */
export const playSessionTransitionSound = (volume: number = 50): void => {
  try {
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    
    // Play two beeps with a short pause between them
    const playBeepAt = (time: number, frequency: number) => {
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.type = 'sine';
      oscillator.frequency.value = frequency;
      gainNode.gain.value = volume / 100;
      
      oscillator.start(time);
      oscillator.stop(time + 0.15); // 150ms beep
    };
    
    const now = audioContext.currentTime;
    playBeepAt(now, 523.25); // C5 note
    playBeepAt(now + 0.25, 659.25); // E5 note
  } catch (error) {
    console.warn('Failed to play session transition sound:', error);
  }
};