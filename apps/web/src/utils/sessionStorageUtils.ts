/**
 * Session storage utilities for the My Pomodoro application
 * 
 * These functions handle storing and retrieving session data from local storage.
 */

import { Session } from '../types/session';

/**
 * Save a session to local storage
 * @param session - The session to save
 */
export const saveSession = (session: Session): void => {
  try {
    const sessions = getSessions();
    sessions.push(session);
    localStorage.setItem('pomodoroSessions', JSON.stringify(sessions));
  } catch (error) {
    console.warn('Failed to save session to local storage:', error);
  }
};

/**
 * Get all sessions from local storage
 * @returns Array of sessions
 */
export const getSessions = (): Session[] => {
  try {
    const sessions = localStorage.getItem('pomodoroSessions');
    return sessions ? JSON.parse(sessions) : [];
  } catch (error) {
    console.warn('Failed to retrieve sessions from local storage:', error);
    return [];
  }
};

/**
 * Clear all sessions from local storage
 */
export const clearSessions = (): void => {
  try {
    localStorage.removeItem('pomodoroSessions');
  } catch (error) {
    console.warn('Failed to clear sessions from local storage:', error);
  }
};